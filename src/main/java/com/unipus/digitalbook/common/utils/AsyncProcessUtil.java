package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 通用异步处理工具类
 * 提供统一的同步/异步处理机制
 *
 * @param <T> 处理结果类型
 */
@Component
@Slf4j
public class AsyncProcessUtil<T> {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // 使用虚拟线程池
    private static final ExecutorService executorService  = Executors.newVirtualThreadPerTaskExecutor();

    private static final long REDIS_EXPIRE_SECONDS = 5*60L;
    private static final String REDIS_KEY_PREFIX = "ASYNC_PROCESS:";
    public static final String PROCESS_STATE_RUNNING = "running";
    public static final String PROCESS_STATE_FINISHED = "finished";
    public static final String PROCESS_STATE_ERROR = "error";

    /**
     * 执行处理任务，支持同步或异步模式
     *
     * @param async          是否同步执行: false为同步执行，true为异步执行
     * @param identifier     标识符，用于日志记录（如实例ID、任务ID等）
     * @param task           要执行的任务
     * @param defaultResult  异步模式下返回的默认结果
     * @return 处理结果
     */
    public T process(boolean async, String identifier, Supplier<T> task, T defaultResult) {
        if (async) {
            // 异步执行 - 直接使用虚拟线程池提交任务
            executorService.execute(() -> {
                try {
                    setProcessState(identifier, PROCESS_STATE_RUNNING);
                    task.get();
                    setProcessState(identifier, PROCESS_STATE_FINISHED);
                    log.debug("异步任务完成: {}", identifier);
                } catch (Exception e) {
                    setProcessState(identifier, PROCESS_STATE_ERROR);
                    log.error("异步任务执行异常: {}", identifier, e);
                }
            });

            // 异步模式下返回默认值
            return defaultResult;
        } else {
            // 同步执行
            try {
                return task.get();
            } catch (Exception e) {
                log.error("同步任务执行异常: {}", identifier, e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 检查处理任务是否正在运行
     *
     * @param identifier 标识符
     * @return true表示正在运行，false表示未运行
     */
    public boolean isProcessRunning(String identifier) {
        return PROCESS_STATE_RUNNING.equals(getProcessState(identifier));
    }

    private String getProcessState(String identifier) {
        return stringRedisTemplate.opsForValue().get(getRedisKey(identifier));
    }

    private void setProcessState(String identifier, String state) {
        stringRedisTemplate.opsForValue().set(getRedisKey(identifier), state, REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }

    private String getRedisKey(String identifier) {
        return REDIS_KEY_PREFIX + identifier;
    }
}